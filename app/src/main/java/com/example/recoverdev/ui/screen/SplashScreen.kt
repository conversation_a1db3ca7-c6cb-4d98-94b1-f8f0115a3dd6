package com.example.recoverdev.ui.screen

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.animation.core.*
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.recoverdev.R
import com.example.recoverdev.viewmodel.SplashState
import com.example.recoverdev.viewmodel.SplashViewModel

@Composable
fun SplashScreen(
    onNavigateToMain: () -> Unit,
    splashViewModel: SplashViewModel = viewModel()
) {
    val state by splashViewModel.state.collectAsState()
    val context = LocalContext.current
    
    LaunchedEffect(state) {
        if (state is SplashState.NavigateToMain) {
            onNavigateToMain()
            splashViewModel.resetNavigationState()
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .paint(painterResource(R.drawable.splash_bg), contentScale = ContentScale.FillBounds)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(modifier = Modifier.fillMaxWidth()
                .weight(1f),
                contentAlignment = Alignment.Center
            ){
                Column {
                    Image(
                        painter = painterResource(id = R.drawable.icon_logo),
                        contentDescription = "App Icon",
                        modifier = Modifier.size(120.dp)
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    // App Title
                    Text(
                        text = stringResource(R.string.app_name),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.first_text_black),
                        textAlign = TextAlign.Center
                    )
                }

            }
            
            when (state) {
                is SplashState.ShowingTermsButton -> {
                    Button(
                        onClick = { splashViewModel.onStartNowClicked() },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.btn_orange)
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.start_now),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.White
                        )
                    }
                }
                
                is SplashState.Loading -> {
                    val loadingState = state as SplashState.Loading
                    
                    val animatedProgress by animateFloatAsState(
                        targetValue = loadingState.progress,
                        animationSpec = tween(
                            durationMillis = 200,
                            easing = FastOutSlowInEasing
                        ),
                        label = "progressAnimation"
                    )
                    
                    val progressAlpha by animateFloatAsState(
                        targetValue = 1f,
                        animationSpec = tween(durationMillis = 300),
                        label = "progressFadeIn"
                    )
                    
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        LinearProgressIndicator(
                            progress = { animatedProgress },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(8.dp)
                                .clip(RoundedCornerShape(4.dp))
                                .alpha(progressAlpha),
                            color = colorResource(R.color.btn_orange),
                            trackColor = Color(0xFFE0E0E0)
                        )
                        
                        Spacer(modifier = Modifier.height(24.dp))
                        
                        Text(
                            text = stringResource(R.string.just_a_moment),
                            fontSize = 16.sp,
                            color = colorResource(R.color.second_text_gray),
                            textAlign = TextAlign.Center
                        )
                    }
                }
                
                else -> {
                    // Empty space for other states
                    Spacer(modifier = Modifier.height(56.dp))
                }
            }
            
            Spacer(modifier = Modifier.height(48.dp))
            
            // Privacy Policy & Terms of Use Links
            PrivacyAndTermsLinks(context = context)
            
            Spacer(modifier = Modifier.height(60.dp))
        }
    }
}

@Composable
private fun PrivacyAndTermsLinks(context: Context) {
    val linkColor = Color(0xFF4B9EF8)
    
    val annotatedString = buildAnnotatedString {
        withStyle(
            style = SpanStyle(
                color = linkColor,
                textDecoration = TextDecoration.Underline,
                fontSize = 14.sp
            )
        ) {
            pushStringAnnotation(tag = "privacy_policy", annotation = "privacy_policy")
            append(stringResource(R.string.privacy_policy))
            pop()
        }
        
        withStyle(
            style = SpanStyle(
                color = colorResource(R.color.first_text_black),
                fontSize = 14.sp
            )
        ) {
            append(" & ")
        }
        
        withStyle(
            style = SpanStyle(
                color = linkColor,
                textDecoration = TextDecoration.Underline,
                fontSize = 14.sp
            )
        ) {
            pushStringAnnotation(tag = "terms_of_use", annotation = "terms_of_use")
            append(stringResource(R.string.terms_of_use))
            pop()
        }
    }
    
    ClickableText(
        text = annotatedString,
        modifier = Modifier.fillMaxWidth(),
        style = androidx.compose.ui.text.TextStyle(
            textAlign = TextAlign.Center,
            lineHeight = 20.sp
        ),
        onClick = { offset ->
            annotatedString.getStringAnnotations(
                tag = "privacy_policy",
                start = offset,
                end = offset
            ).firstOrNull()?.let {
                openPrivacyPolicy(context)
            }
            
            annotatedString.getStringAnnotations(
                tag = "terms_of_use", 
                start = offset,
                end = offset
            ).firstOrNull()?.let {
                openTermsOfService(context)
            }
        }
    )
}

private fun openPrivacyPolicy(context: Context) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(context.getString(R.string.privacy_policy_url)))
    try {
        context.startActivity(intent)
    } catch (e: Exception) {
        // Handle error
    }
}

private fun openTermsOfService(context: Context) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(context.getString(R.string.terms_of_service_url)))
    try {
        context.startActivity(intent)
    } catch (e: Exception) {
        // Handle error
    }
}